package ai.friday.dda.adapters.jobs

import ai.friday.dda.adapters.arbi.DDABillsBatchProcessor
import ai.friday.dda.andAppend
import ai.friday.dda.app.BrazilZonedDateTimeSupplier
import ai.friday.dda.app.interfaces.ObjectRepository
import ai.friday.dda.app.job.AbstractJob
import io.micronaut.context.annotation.EachBean
import io.micronaut.context.annotation.Property
import io.micronaut.context.annotation.Requires
import io.micronaut.tracing.annotation.NewSpan
import jakarta.inject.Singleton
import java.time.format.DateTimeFormatter
import java.util.zip.GZIPInputStream
import net.logstash.logback.marker.Markers.append
import org.slf4j.LoggerFactory

@EachBean(TenantConfiguration::class)
@Requires(property = "features.dda.files.enabled", value = "true", notEnv = ["staging"])
open class ProcessDDAFilesWithErrorJob(
    private val objectRepository: ObjectRepository,
    private val ddaBillsBatchProcessor: DDABillsBatchProcessor,
    private val configuration: TenantConfiguration
) : AbstractJob(cron = "30 * * * *") {

    @NewSpan
    override fun execute() {
        logger.info("iniciando ProcessDDAFilesWithErrorJob")

        val unprocessedFiles = objectRepository.listObjectKeys(configuration.bucketName, directoryKey = "errors")
        for (file in unprocessedFiles) {
            val markers = append("filename", file)

            logger.info(markers, "ProcessDDAFilesWithErrorJob")

            val loadedFile = objectRepository.loadObject(configuration.bucketName, file)
            val unzippedFile = GZIPInputStream(loadedFile)
            val result = ddaBillsBatchProcessor.processFileWithError(unzippedFile, configuration.name)

            val today = BrazilZonedDateTimeSupplier.getLocalDate()
            val s3FolderName = today.format(DateTimeFormatter.ofPattern("yyyyMMdd"))

            if (result.isSuccess) {
                objectRepository.moveObject(
                    configuration.bucketName,
                    file,
                    toKey = "processed/$s3FolderName/${file.substringAfterLast("/")}"
                )

                logger.info(markers.andAppend("result", "success"), "ProcessDDAFilesWithErrorJob")
            } else {
                logger.error(
                    markers.andAppend("result", "error"),
                    "ProcessDDAFilesWithErrorJob",
                    result.exceptionOrNull()
                )
            }
        }
    }

    companion object {
        private val logger = LoggerFactory.getLogger(ProcessDDAFilesWithErrorJob::class.java)
    }
}