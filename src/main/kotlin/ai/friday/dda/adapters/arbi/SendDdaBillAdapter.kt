package ai.friday.dda.adapters.arbi

import ai.friday.dda.adapters.jobs.TenantConfiguration
import io.micronaut.core.type.Argument
import io.micronaut.http.HttpRequest
import io.micronaut.http.HttpVersion
import io.micronaut.http.client.HttpClient
import io.micronaut.http.client.HttpClientRegistry
import jakarta.inject.Singleton

@Singleton
class SendDdaBillAdapter(
    private val cli: HttpClientRegistry<HttpClient>,
    private val config: Map<String, TenantConfiguration>,
) {
    fun send(list: List<DDABillTO>, tenantName: TenantName): Result<Boolean> {
        val client = cli.getClient(HttpVersion.HTTP_1_1, tenantName.name, null)
        val configuration = config[tenantName.name.lowercase()]?.configuration as? ResponseConfiguration.HttpConfiguration

        if (client == null || configuration == null) {
            throw IllegalStateException("Client not found for tenant $tenantName")
        }

        val httpRequest = HttpRequest.POST("/dda", list)
            .basicAuth(configuration.username, configuration.password)

        val response = client.toBlocking().exchange(
            httpRequest,
            Argument.STRING,
            Argument.STRING,
        )
        TODO()
    }

    fun sendError(response: String, tenantName: TenantName): Result<Boolean> {
        TODO("Not yet implemented")
    }
}